from flask import Flask, render_template, request, jsonify
from flask_socketio import <PERSON><PERSON><PERSON>, emit
import cv2
import numpy as np
import base64
import threading
import time

# Try to import advanced face swapper, fallback to simple version
try:
    from advanced_face_swapper import AdvancedFaceSwapper as FaceSwapper
    print("Using advanced face swapper with dlib landmarks")
except ImportError:
    try:
        import mediapipe as mp
        from face_swapper import FaceSwapper
        print("Using MediaPipe face swapper")
    except ImportError:
        from face_swapper_simple import FaceSwapper
        print("Using simple face swapper with OpenCV only")

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
socketio = SocketIO(app, cors_allowed_origins="*")

# Initialize face swapper
face_swapper = FaceSwapper()

# Global variables for video processing
current_frame = None
target_face_encoding = None
target_face_image = None
source_face_encoding = None
swap_enabled = False

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/upload_target_face', methods=['POST'])
def upload_target_face():
    """Upload target face image for face swapping"""
    global target_face_encoding, target_face_image

    if 'file' not in request.files:
        return jsonify({'error': 'No file uploaded'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400

    # Read and process the uploaded image
    file_bytes = np.frombuffer(file.read(), np.uint8)
    image = cv2.imdecode(file_bytes, cv2.IMREAD_COLOR)

    # Extract face encoding and store the image
    encoding = face_swapper.get_face_encoding(image)
    if encoding is not None:
        target_face_encoding = encoding
        target_face_image = image.copy()
        return jsonify({'success': 'Target face uploaded successfully'})
    else:
        return jsonify({'error': 'No face detected in the uploaded image'}), 400

@socketio.on('video_frame')
def handle_video_frame(data):
    """Handle incoming video frames from client"""
    global current_frame, swap_enabled
    
    try:
        # Decode base64 image
        image_data = base64.b64decode(data['image'].split(',')[1])
        nparr = np.frombuffer(image_data, np.uint8)
        frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if frame is not None:
            processed_frame = frame.copy()
            
            # Perform face swapping if enabled and target face is available
            if swap_enabled and target_face_image is not None:
                processed_frame = face_swapper.swap_faces(frame, target_face_image)
            
            # Encode processed frame back to base64
            _, buffer = cv2.imencode('.jpg', processed_frame)
            processed_image = base64.b64encode(buffer).decode('utf-8')
            
            # Send processed frame back to client
            emit('processed_frame', {'image': f'data:image/jpeg;base64,{processed_image}'})
    
    except Exception as e:
        print(f"Error processing frame: {e}")

@socketio.on('toggle_swap')
def handle_toggle_swap(data):
    """Toggle face swapping on/off"""
    global swap_enabled
    swap_enabled = data.get('enabled', False)
    emit('swap_status', {'enabled': swap_enabled})

@socketio.on('connect')
def handle_connect():
    print('Client connected')
    emit('connected', {'data': 'Connected to face swap server'})

@socketio.on('disconnect')
def handle_disconnect():
    print('Client disconnected')

if __name__ == '__main__':
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
