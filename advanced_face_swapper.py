import cv2
import numpy as np
import dlib
from scipy.spatial import distance
from scipy.spatial.distance import euclidean
import math

class AdvancedFaceSwapper:
    """Advanced face swapper with realistic alignment and blending"""
    
    def __init__(self):
        # Initialize dlib face detector and predictor
        self.detector = dlib.get_frontal_face_detector()
        
        # Try to load the shape predictor
        try:
            self.predictor = dlib.shape_predictor('shape_predictor_68_face_landmarks.dat')
            self.use_dlib = True
            print("Using dlib with 68-point landmarks for advanced face swapping")
        except:
            print("Warning: shape_predictor_68_face_landmarks.dat not found")
            print("Falling back to OpenCV-based face swapping")
            self.use_dlib = False
            self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        
        # Face landmark indices for different facial features
        self.FACIAL_LANDMARKS_IDXS = {
            "mouth": (48, 68),
            "right_eyebrow": (17, 22),
            "left_eyebrow": (22, 27),
            "right_eye": (36, 42),
            "left_eye": (42, 48),
            "nose": (27, 35),
            "jaw": (0, 17)
        }
        
        # Key points for face alignment
        self.ALIGN_POINTS = [
            30,  # Nose tip
            8,   # Chin
            36,  # Left eye left corner
            45,  # Right eye right corner
            48,  # Left mouth corner
            54   # Right mouth corner
        ]
    
    def get_landmarks(self, image):
        """Extract 68 facial landmarks using dlib"""
        if not self.use_dlib:
            return None
            
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        faces = self.detector(gray)
        
        if len(faces) == 0:
            return None
        
        # Use the first detected face
        face = faces[0]
        landmarks = self.predictor(gray, face)
        
        # Convert to numpy array
        coords = np.zeros((68, 2), dtype=np.int32)
        for i in range(68):
            coords[i] = (landmarks.part(i).x, landmarks.part(i).y)
        
        return coords
    
    def get_face_mask(self, image, landmarks):
        """Create precise face mask using facial landmarks"""
        mask = np.zeros(image.shape[:2], dtype=np.uint8)
        
        # Define face boundary points (jawline + forehead)
        face_points = []
        
        # Jawline (0-16)
        for i in range(17):
            face_points.append(landmarks[i])
        
        # Add forehead points (estimated)
        # Left forehead
        left_eyebrow_left = landmarks[17]
        left_eyebrow_right = landmarks[21]
        left_forehead = (left_eyebrow_left[0] - 20, left_eyebrow_left[1] - 30)
        face_points.append(left_forehead)
        
        # Top forehead
        nose_bridge_top = landmarks[27]
        top_forehead = (nose_bridge_top[0], nose_bridge_top[1] - 50)
        face_points.append(top_forehead)
        
        # Right forehead
        right_eyebrow_left = landmarks[22]
        right_eyebrow_right = landmarks[26]
        right_forehead = (right_eyebrow_right[0] + 20, right_eyebrow_right[1] - 30)
        face_points.append(right_forehead)
        
        # Create convex hull for smooth face boundary
        face_points = np.array(face_points, dtype=np.int32)
        hull = cv2.convexHull(face_points)
        cv2.fillPoly(mask, [hull], 255)
        
        # Smooth the mask
        mask = cv2.GaussianBlur(mask, (15, 15), 0)
        
        return mask
    
    def align_face(self, source_landmarks, target_landmarks):
        """Calculate transformation matrix for precise face alignment"""
        # Use key facial points for alignment
        source_points = source_landmarks[self.ALIGN_POINTS].astype(np.float32)
        target_points = target_landmarks[self.ALIGN_POINTS].astype(np.float32)
        
        # Calculate similarity transformation (rotation, scale, translation)
        transformation_matrix = cv2.estimateAffinePartial2D(source_points, target_points)[0]
        
        return transformation_matrix
    
    def color_correct_face(self, source_face, target_face, landmarks):
        """Apply color correction to match skin tones"""
        try:
            # Define skin regions using landmarks
            left_cheek = landmarks[1:6]  # Left jawline
            right_cheek = landmarks[11:16]  # Right jawline
            forehead_center = landmarks[27]  # Nose bridge
            
            # Create masks for skin regions
            mask = np.zeros(source_face.shape[:2], dtype=np.uint8)
            
            # Left cheek
            cv2.fillPoly(mask, [left_cheek], 255)
            # Right cheek  
            cv2.fillPoly(mask, [right_cheek], 255)
            
            # Calculate average colors in skin regions
            source_skin = cv2.mean(source_face, mask=mask)[:3]
            target_skin = cv2.mean(target_face, mask=mask)[:3]
            
            # Calculate color correction ratios
            color_ratios = np.array(target_skin) / (np.array(source_skin) + 1e-6)
            
            # Apply color correction
            corrected_face = source_face.astype(np.float32)
            for i in range(3):
                corrected_face[:, :, i] *= color_ratios[i]
            
            corrected_face = np.clip(corrected_face, 0, 255).astype(np.uint8)
            
            return corrected_face
            
        except Exception as e:
            print(f"Color correction failed: {e}")
            return source_face
    
    def seamless_blend(self, source_face, target_image, mask, center):
        """Apply seamless cloning for natural blending"""
        try:
            # Use Poisson blending for seamless integration
            result = cv2.seamlessClone(
                source_face, 
                target_image, 
                mask, 
                center, 
                cv2.NORMAL_CLONE
            )
            return result
        except Exception as e:
            print(f"Seamless blending failed, using normal blending: {e}")
            return self.normal_blend(source_face, target_image, mask)
    
    def normal_blend(self, source_face, target_image, mask):
        """Fallback normal blending method"""
        mask_3channel = cv2.merge([mask, mask, mask])
        mask_normalized = mask_3channel.astype(np.float32) / 255.0
        
        # Feather the mask edges
        mask_normalized = cv2.GaussianBlur(mask_normalized, (15, 15), 0)
        
        result = source_face.astype(np.float32) * mask_normalized + \
                target_image.astype(np.float32) * (1 - mask_normalized)
        
        return result.astype(np.uint8)
    
    def enhance_face_details(self, face_region):
        """Enhance facial details for more realistic appearance"""
        try:
            # Apply subtle sharpening
            kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
            sharpened = cv2.filter2D(face_region, -1, kernel)
            
            # Blend original and sharpened (subtle enhancement)
            enhanced = cv2.addWeighted(face_region, 0.7, sharpened, 0.3, 0)
            
            # Apply bilateral filter to smooth skin while preserving edges
            smoothed = cv2.bilateralFilter(enhanced, 9, 75, 75)
            
            return smoothed
            
        except Exception as e:
            print(f"Face enhancement failed: {e}")
            return face_region
    
    def swap_faces(self, frame, target_face_image):
        """Main advanced face swapping function"""
        if target_face_image is None:
            return frame
        
        try:
            # Get landmarks for both faces
            frame_landmarks = self.get_landmarks(frame)
            target_landmarks = self.get_landmarks(target_face_image)
            
            if frame_landmarks is None or target_landmarks is None:
                # Fallback to simple detection
                return self.simple_face_swap_fallback(frame, target_face_image)
            
            # Calculate transformation matrix
            transformation_matrix = self.align_face(target_landmarks, frame_landmarks)
            
            if transformation_matrix is None:
                return frame
            
            # Transform target face to align with frame face
            aligned_target = cv2.warpAffine(
                target_face_image,
                transformation_matrix,
                (frame.shape[1], frame.shape[0])
            )
            
            # Transform target landmarks
            target_landmarks_transformed = cv2.transform(
                target_landmarks.reshape(-1, 1, 2).astype(np.float32),
                transformation_matrix
            ).reshape(-1, 2).astype(np.int32)
            
            # Create precise face mask
            mask = self.get_face_mask(frame, frame_landmarks)
            
            # Extract face regions for color correction
            face_rect = cv2.boundingRect(frame_landmarks)
            x, y, w, h = face_rect
            
            frame_face_region = frame[y:y+h, x:x+w]
            aligned_face_region = aligned_target[y:y+h, x:x+w]
            
            # Apply color correction
            color_corrected_face = self.color_correct_face(
                aligned_face_region, 
                frame_face_region, 
                frame_landmarks - [x, y]
            )
            
            # Enhance face details
            enhanced_face = self.enhance_face_details(color_corrected_face)
            
            # Put enhanced face back into aligned target
            aligned_target[y:y+h, x:x+w] = enhanced_face
            
            # Calculate center for seamless cloning
            M = cv2.moments(mask)
            if M["m00"] != 0:
                center = (int(M["m10"] / M["m00"]), int(M["m01"] / M["m00"]))
            else:
                center = (frame.shape[1] // 2, frame.shape[0] // 2)
            
            # Apply seamless blending
            result = self.seamless_blend(aligned_target, frame, mask, center)
            
            # Add debug information
            cv2.putText(result, "Advanced Face Swap Active", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            return result
            
        except Exception as e:
            print(f"Advanced face swap failed: {e}")
            return self.simple_face_swap_fallback(frame, target_face_image)
    
    def simple_face_swap_fallback(self, frame, target_face_image):
        """Fallback to simple face swapping if advanced method fails"""
        try:
            # Use OpenCV face detection
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            faces = self.face_cascade.detectMultiScale(gray, 1.1, 4)
            
            if len(faces) == 0:
                cv2.putText(frame, "No face detected", (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                return frame
            
            # Use the largest detected face
            face = max(faces, key=lambda x: x[2] * x[3])
            x, y, w, h = face
            
            # Resize target face to match detected face
            target_resized = cv2.resize(target_face_image, (w, h))
            
            # Create simple elliptical mask
            mask = np.zeros((h, w), dtype=np.uint8)
            center = (w//2, h//2)
            axes = (w//3, h//2)
            cv2.ellipse(mask, center, axes, 0, 0, 360, 255, -1)
            mask = cv2.GaussianBlur(mask, (15, 15), 0)
            
            # Blend faces
            mask_3channel = cv2.merge([mask, mask, mask]).astype(np.float32) / 255.0
            
            face_region = frame[y:y+h, x:x+w]
            blended = target_resized.astype(np.float32) * mask_3channel + \
                     face_region.astype(np.float32) * (1 - mask_3channel)
            
            frame[y:y+h, x:x+w] = blended.astype(np.uint8)
            
            cv2.putText(frame, "Simple Face Swap Active", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
            
            return frame
            
        except Exception as e:
            print(f"Simple face swap fallback failed: {e}")
            return frame

# For backward compatibility
class FaceSwapper(AdvancedFaceSwapper):
    """Alias for AdvancedFaceSwapper"""
    pass
