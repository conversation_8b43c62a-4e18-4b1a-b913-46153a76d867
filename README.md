# Real-Time Face Swap Application

A sophisticated Python Flask application that performs real-time face swapping in live video streams, similar to video conferencing applications like Skype or Teams. This application allows you to replace one person's face with another person's face in real-time during video calls or presentations.

## Features

- **Real-time face detection and swapping** using MediaPipe and OpenCV
- **Modern web interface** with Bootstrap and responsive design
- **Live video streaming** with WebSocket communication
- **Multiple camera support** with camera switching functionality
- **Face landmark detection** for accurate face alignment
- **Smooth face blending** with advanced image processing
- **Real-time performance monitoring** with FPS counter
- **Easy target face upload** through web interface

## Use Cases

- **Virtual presentations**: Person A can give a presentation while appearing as Person B
- **Video conferencing**: Replace your face with another person's face during calls
- **Content creation**: Create entertaining videos with face swaps
- **Privacy protection**: Use a different face for online meetings

## Requirements

- Python 3.7 or higher
- Webcam or camera device
- Modern web browser with WebRTC support
- At least 4GB RAM (8GB recommended for better performance)

## Installation

### Prerequisites

1. **Install Python 3.7+**: Download from [python.org](https://www.python.org/downloads/)
   - Make sure to check "Add Python to PATH" during installation
   - Verify installation: `py --version`

2. **Install pip** (if not included):
   ```bash
   py -m ensurepip --upgrade
   ```

### Setup Steps

1. **Clone or download this repository**

2. **Install dependencies**:
   ```bash
   # Basic dependencies
   py -m pip install Flask opencv-python numpy Pillow flask-socketio

   # Advanced face processing (optional, for better results)
   py -m pip install mediapipe face-recognition dlib scipy
   ```

3. **Download face landmarks model** (optional, for advanced features):
   - Download: [shape_predictor_68_face_landmarks.dat.bz2](http://dlib.net/files/shape_predictor_68_face_landmarks.dat.bz2)
   - Extract and place `shape_predictor_68_face_landmarks.dat` in project root

### Quick Start (Minimal Setup)

If you want to test quickly with basic functionality:
```bash
py -m pip install Flask opencv-python numpy Pillow flask-socketio mediapipe
py app.py
```

## Usage

### Starting the Application

1. Run the Flask application:
   ```bash
   python app.py
   ```

2. Open your web browser and navigate to:
   ```
   http://localhost:5000
   ```

### Using Face Swap

1. **Upload Target Face**:
   - Click "Choose File" in the Target Face section
   - Select a clear image of the person whose face you want to use
   - Click "Upload Target Face"
   - Wait for confirmation message

2. **Start Camera**:
   - Click "Start Camera" to begin video capture
   - Allow camera permissions when prompted
   - Your live video will appear in the "Original Video" section

3. **Enable Face Swap**:
   - Toggle the "Enable Face Swap" switch
   - The processed video with face swap will appear in the "Face Swapped Video" section

4. **Adjust Settings**:
   - Use the confidence slider to adjust face detection sensitivity
   - Switch between cameras if multiple cameras are available

### Tips for Best Results

- **Good lighting**: Ensure the face is well-lit and clearly visible
- **Clear target image**: Use a high-quality image with a single, front-facing face
- **Stable position**: Keep your head relatively stable for better tracking
- **Similar face angles**: Target face and live face should have similar angles for best results

## Technical Details

### Architecture

- **Backend**: Flask with SocketIO for real-time communication
- **Frontend**: HTML5, CSS3, JavaScript with WebRTC
- **Computer Vision**: OpenCV, MediaPipe, dlib
- **Face Recognition**: face_recognition library
- **Image Processing**: NumPy, SciPy, Pillow

### Face Swapping Process

1. **Face Detection**: MediaPipe detects faces in real-time video
2. **Landmark Extraction**: 468 facial landmarks are identified
3. **Face Alignment**: Target face is aligned to match live face orientation
4. **Mask Creation**: Precise face mask is generated for seamless blending
5. **Face Blending**: Advanced blending techniques merge the faces naturally

### Performance Optimization

- **Real-time processing**: Optimized for 30 FPS performance
- **Efficient algorithms**: Uses hardware acceleration where available
- **Memory management**: Proper cleanup to prevent memory leaks
- **Adaptive quality**: Automatically adjusts processing based on performance

## File Structure

```
face-swap/
├── app.py                 # Main Flask application
├── face_swapper.py        # Core face swapping logic
├── requirements.txt       # Python dependencies
├── setup.py              # Setup and installation script
├── README.md             # This file
├── templates/
│   └── index.html        # Main web interface
├── static/
│   ├── style.css         # CSS styles
│   └── app.js            # Frontend JavaScript
└── shape_predictor_68_face_landmarks.dat  # dlib model (downloaded)
```

## Troubleshooting

### Camera Issues (Most Common)

If you get "Error accessing camera" message:

**Quick Fix:**
```bash
# Run the camera diagnostic
py camera_test.py

# Or use the quick fix script (Windows)
fix_camera_issues.bat
```

**Manual Steps:**
1. **Browser Permissions**: Allow camera access when prompted
2. **Close Other Apps**: Skype, Teams, Zoom, Camera app
3. **Windows Settings**: Settings > Privacy > Camera > Allow apps to access camera
4. **Try Different Browser**: Chrome, Firefox, Edge
5. **Restart Computer**: Often fixes driver issues

**Detailed Help**: See `CAMERA_TROUBLESHOOTING.md` for comprehensive solutions.

### Other Common Issues

1. **Face not detected**:
   - Ensure good lighting
   - Face should be clearly visible and front-facing
   - Adjust detection confidence slider

2. **Poor performance**:
   - Close other applications to free up resources
   - Lower video resolution in browser settings
   - Ensure good CPU performance

3. **Installation issues**:
   - Make sure Python 3.7+ is installed
   - Try installing dependencies individually
   - Check for system-specific requirements

### Browser Compatibility

- **Chrome**: Fully supported
- **Firefox**: Fully supported
- **Safari**: Supported (may need additional permissions)
- **Edge**: Fully supported

## Security and Privacy

- **Local processing**: All face processing happens locally on your machine
- **No data storage**: Images are not permanently stored
- **Secure connections**: Uses HTTPS in production environments
- **Privacy first**: No face data is sent to external servers

## Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.

## License

This project is open source and available under the MIT License.

## Disclaimer

This application is for educational and entertainment purposes. Please respect privacy and obtain consent before using someone's likeness. The developers are not responsible for misuse of this technology.
