// Global variables
let socket;
let videoStream;
let originalVideo;
let originalCanvas;
let processedVideo;
let isStreaming = false;
let currentCameraIndex = 0;
let availableCameras = [];
let fpsCounter = 0;
let lastFrameTime = Date.now();

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeElements();
    initializeSocket();
    setupEventListeners();
    detectCameras();
});

function initializeElements() {
    originalVideo = document.getElementById('originalVideo');
    originalCanvas = document.getElementById('originalCanvas');
    processedVideo = document.getElementById('processedVideo');
    
    // Set canvas size
    originalCanvas.width = 640;
    originalCanvas.height = 480;
}

function initializeSocket() {
    socket = io();
    
    socket.on('connect', function() {
        console.log('Connected to server');
        updateConnectionStatus('Connected', 'success');
    });
    
    socket.on('disconnect', function() {
        console.log('Disconnected from server');
        updateConnectionStatus('Disconnected', 'danger');
    });
    
    socket.on('processed_frame', function(data) {
        if (processedVideo && data.image) {
            processedVideo.src = data.image;
            updateFPS();
        }
    });
    
    socket.on('swap_status', function(data) {
        updateSwapStatus(data.enabled);
    });
}

function setupEventListeners() {
    // Camera controls
    document.getElementById('startCameraBtn').addEventListener('click', startCamera);
    document.getElementById('stopCameraBtn').addEventListener('click', stopCamera);
    document.getElementById('switchCameraBtn').addEventListener('click', switchCamera);
    
    // Face swap controls
    document.getElementById('enableSwapToggle').addEventListener('change', toggleFaceSwap);
    document.getElementById('uploadTargetBtn').addEventListener('click', uploadTargetFace);
    
    // Confidence slider
    const confidenceSlider = document.getElementById('confidenceSlider');
    confidenceSlider.addEventListener('input', function() {
        document.getElementById('confidenceValue').textContent = this.value;
    });
}

async function detectCameras() {
    try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        availableCameras = devices.filter(device => device.kind === 'videoinput');
        console.log('Available cameras:', availableCameras.length);
    } catch (error) {
        console.error('Error detecting cameras:', error);
    }
}

async function startCamera() {
    try {
        // Show loading state
        const startBtn = document.getElementById('startCameraBtn');
        const originalText = startBtn.innerHTML;
        startBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Starting Camera...';
        startBtn.disabled = true;

        // Check if getUserMedia is supported
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            throw new Error('Camera API not supported in this browser');
        }

        const constraints = {
            video: {
                deviceId: availableCameras.length > 0 ?
                    { exact: availableCameras[currentCameraIndex].deviceId } : undefined,
                width: { ideal: 640 },
                height: { ideal: 480 },
                frameRate: { ideal: 30 }
            },
            audio: false
        };

        videoStream = await navigator.mediaDevices.getUserMedia(constraints);
        originalVideo.srcObject = videoStream;

        originalVideo.onloadedmetadata = function() {
            originalVideo.play();
            isStreaming = true;
            startVideoProcessing();

            // Update UI
            document.getElementById('stopCameraBtn').disabled = false;
            originalVideo.classList.add('recording');

            showCameraStatus('Camera started successfully', 'success');
        };

    } catch (error) {
        console.error('Error starting camera:', error);

        // Restore button state
        const startBtn = document.getElementById('startCameraBtn');
        startBtn.innerHTML = '<i class="fas fa-video"></i> Start Camera';
        startBtn.disabled = false;

        // Show detailed error message
        let errorMessage = 'Error accessing camera. ';

        if (error.name === 'NotAllowedError') {
            errorMessage += 'Camera permission denied. Please allow camera access and try again.';
            showCameraHelp('permission');
        } else if (error.name === 'NotFoundError') {
            errorMessage += 'No camera found. Please connect a camera and try again.';
            showCameraHelp('notfound');
        } else if (error.name === 'NotReadableError') {
            errorMessage += 'Camera is being used by another application. Please close other camera apps and try again.';
            showCameraHelp('inuse');
        } else if (error.name === 'OverconstrainedError') {
            errorMessage += 'Camera does not support the requested settings. Trying with default settings...';
            // Try with simpler constraints
            setTimeout(() => startCameraWithFallback(), 1000);
            return;
        } else {
            errorMessage += error.message || 'Unknown error occurred.';
        }

        showCameraStatus(errorMessage, 'danger');
    }
}

function stopCamera() {
    if (videoStream) {
        videoStream.getTracks().forEach(track => track.stop());
        videoStream = null;
    }
    
    originalVideo.srcObject = null;
    isStreaming = false;
    
    // Update UI
    document.getElementById('startCameraBtn').disabled = false;
    document.getElementById('stopCameraBtn').disabled = true;
    originalVideo.classList.remove('recording');
    processedVideo.src = '';
}

async function switchCamera() {
    if (availableCameras.length <= 1) {
        alert('Only one camera available');
        return;
    }
    
    currentCameraIndex = (currentCameraIndex + 1) % availableCameras.length;
    
    if (isStreaming) {
        stopCamera();
        setTimeout(startCamera, 500); // Small delay to ensure cleanup
    }
}

function startVideoProcessing() {
    if (!isStreaming) return;
    
    const ctx = originalCanvas.getContext('2d');
    
    function processFrame() {
        if (!isStreaming) return;
        
        // Draw current video frame to canvas
        ctx.drawImage(originalVideo, 0, 0, originalCanvas.width, originalCanvas.height);
        
        // Convert canvas to base64 image
        const imageData = originalCanvas.toDataURL('image/jpeg', 0.8);
        
        // Send frame to server for processing
        if (socket && socket.connected) {
            socket.emit('video_frame', { image: imageData });
        }
        
        // Continue processing
        requestAnimationFrame(processFrame);
    }
    
    processFrame();
}

function toggleFaceSwap() {
    const enabled = document.getElementById('enableSwapToggle').checked;
    
    if (socket && socket.connected) {
        socket.emit('toggle_swap', { enabled: enabled });
    }
    
    updateSwapStatus(enabled);
}

function uploadTargetFace() {
    const fileInput = document.getElementById('targetFaceInput');
    const file = fileInput.files[0];
    
    if (!file) {
        showUploadStatus('Please select a file first', 'danger');
        return;
    }
    
    const formData = new FormData();
    formData.append('file', file);
    
    // Show loading state
    const uploadBtn = document.getElementById('uploadTargetBtn');
    const originalText = uploadBtn.innerHTML;
    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';
    uploadBtn.disabled = true;
    
    fetch('/upload_target_face', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showUploadStatus(data.success, 'success');
        } else {
            showUploadStatus(data.error || 'Upload failed', 'danger');
        }
    })
    .catch(error => {
        console.error('Upload error:', error);
        showUploadStatus('Upload failed. Please try again.', 'danger');
    })
    .finally(() => {
        // Restore button state
        uploadBtn.innerHTML = originalText;
        uploadBtn.disabled = false;
    });
}

function showUploadStatus(message, type) {
    const statusDiv = document.getElementById('uploadStatus');
    statusDiv.innerHTML = `<div class="alert alert-${type} mt-2">${message}</div>`;
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
        statusDiv.innerHTML = '';
    }, 5000);
}

function updateConnectionStatus(status, type) {
    const statusElement = document.getElementById('connectionStatus');
    statusElement.textContent = status;
    statusElement.className = `badge bg-${type}`;
}

function updateSwapStatus(enabled) {
    const statusElement = document.getElementById('swapStatus');
    statusElement.textContent = enabled ? 'Enabled' : 'Disabled';
    statusElement.className = `badge bg-${enabled ? 'success' : 'secondary'}`;
}

function updateFPS() {
    const now = Date.now();
    const timeDiff = now - lastFrameTime;
    
    if (timeDiff > 0) {
        const fps = Math.round(1000 / timeDiff);
        document.getElementById('fpsCounter').textContent = fps;
    }
    
    lastFrameTime = now;
}

// Handle page visibility changes
document.addEventListener('visibilitychange', function() {
    if (document.hidden && isStreaming) {
        // Pause processing when tab is not visible to save resources
        console.log('Tab hidden, pausing video processing');
    } else if (!document.hidden && isStreaming) {
        // Resume processing when tab becomes visible
        console.log('Tab visible, resuming video processing');
    }
});

// Handle window resize
window.addEventListener('resize', function() {
    // Adjust video container sizes if needed
    console.log('Window resized');
});

// Error handling for video element
originalVideo.addEventListener('error', function(e) {
    console.error('Video error:', e);
    showCameraStatus('Video playback error. Please try restarting the camera.', 'danger');
});

// Helper functions for camera troubleshooting
async function startCameraWithFallback() {
    try {
        // Try with minimal constraints
        const constraints = {
            video: true,
            audio: false
        };

        videoStream = await navigator.mediaDevices.getUserMedia(constraints);
        originalVideo.srcObject = videoStream;

        originalVideo.onloadedmetadata = function() {
            originalVideo.play();
            isStreaming = true;
            startVideoProcessing();

            // Update UI
            document.getElementById('startCameraBtn').disabled = true;
            document.getElementById('stopCameraBtn').disabled = false;
            originalVideo.classList.add('recording');

            showCameraStatus('Camera started with fallback settings', 'warning');
        };

    } catch (error) {
        console.error('Fallback camera start failed:', error);
        showCameraStatus('Camera initialization failed completely. Please check troubleshooting guide.', 'danger');
        showCameraHelp('failed');
    }
}

function showCameraStatus(message, type) {
    // Create or update status message
    let statusDiv = document.getElementById('cameraStatus');
    if (!statusDiv) {
        statusDiv = document.createElement('div');
        statusDiv.id = 'cameraStatus';
        statusDiv.className = 'mt-3';
        document.querySelector('.col-md-9').insertBefore(statusDiv, document.querySelector('.row').nextSibling);
    }

    statusDiv.innerHTML = `<div class="alert alert-${type} alert-dismissible fade show" role="alert">
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>`;

    // Auto-hide success messages
    if (type === 'success') {
        setTimeout(() => {
            if (statusDiv) statusDiv.innerHTML = '';
        }, 5000);
    }
}

function showCameraHelp(errorType) {
    const helpMessages = {
        permission: `
            <strong>Camera Permission Denied:</strong><br>
            1. Click the camera icon in your browser's address bar<br>
            2. Select "Always allow" for camera access<br>
            3. Refresh the page and try again<br>
            4. Check Windows Privacy Settings: Settings > Privacy > Camera
        `,
        notfound: `
            <strong>No Camera Found:</strong><br>
            1. Check if your camera is properly connected<br>
            2. Try a different USB port<br>
            3. Update camera drivers in Device Manager<br>
            4. Test camera with other applications (Camera app)
        `,
        inuse: `
            <strong>Camera In Use:</strong><br>
            1. Close Skype, Teams, Zoom, or other video apps<br>
            2. Close other browser tabs using camera<br>
            3. Restart your browser<br>
            4. Check Task Manager for camera processes
        `,
        failed: `
            <strong>Camera Failed to Initialize:</strong><br>
            1. Run the camera diagnostic: <code>py camera_test.py</code><br>
            2. Check the CAMERA_TROUBLESHOOTING.md file<br>
            3. Try a different browser<br>
            4. Restart your computer
        `
    };

    const helpMessage = helpMessages[errorType] || 'Unknown camera error. Please check the troubleshooting guide.';

    // Show help in a modal or expandable section
    setTimeout(() => {
        const helpDiv = document.createElement('div');
        helpDiv.innerHTML = `
            <div class="alert alert-info mt-2">
                <h6><i class="fas fa-question-circle"></i> Troubleshooting Help:</h6>
                ${helpMessage}
                <hr>
                <small>For more help, see <strong>CAMERA_TROUBLESHOOTING.md</strong> or run <strong>camera_test.py</strong></small>
            </div>
        `;

        const statusDiv = document.getElementById('cameraStatus');
        if (statusDiv) {
            statusDiv.appendChild(helpDiv);
        }
    }, 1000);
}
