#!/usr/bin/env python3
"""
Camera diagnostic tool to help troubleshoot camera access issues
"""

import cv2
import sys
import time

def test_camera_basic():
    """Test basic camera access"""
    print("Testing basic camera access...")
    
    try:
        # Try to open default camera
        cap = cv2.VideoCapture(0)
        
        if not cap.isOpened():
            print("❌ Cannot open camera 0")
            return False
        
        # Try to read a frame
        ret, frame = cap.read()
        
        if not ret:
            print("❌ Cannot read frame from camera")
            cap.release()
            return False
        
        print(f"✅ Camera working! Frame size: {frame.shape}")
        cap.release()
        return True
        
    except Exception as e:
        print(f"❌ Camera test failed: {e}")
        return False

def test_multiple_cameras():
    """Test multiple camera indices"""
    print("\nTesting multiple camera indices...")
    
    working_cameras = []
    
    for i in range(5):  # Test cameras 0-4
        try:
            cap = cv2.VideoCapture(i)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret:
                    print(f"✅ Camera {i}: Working - {frame.shape}")
                    working_cameras.append(i)
                else:
                    print(f"⚠️ Camera {i}: Opens but cannot read frames")
                cap.release()
            else:
                print(f"❌ Camera {i}: Cannot open")
        except Exception as e:
            print(f"❌ Camera {i}: Error - {e}")
    
    return working_cameras

def test_camera_properties():
    """Test camera properties and capabilities"""
    print("\nTesting camera properties...")
    
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("❌ Cannot open camera for property testing")
        return
    
    properties = {
        'Width': cv2.CAP_PROP_FRAME_WIDTH,
        'Height': cv2.CAP_PROP_FRAME_HEIGHT,
        'FPS': cv2.CAP_PROP_FPS,
        'Brightness': cv2.CAP_PROP_BRIGHTNESS,
        'Contrast': cv2.CAP_PROP_CONTRAST,
        'Saturation': cv2.CAP_PROP_SATURATION,
        'Hue': cv2.CAP_PROP_HUE,
    }
    
    print("Camera properties:")
    for name, prop in properties.items():
        value = cap.get(prop)
        print(f"  {name}: {value}")
    
    cap.release()

def test_camera_with_display():
    """Test camera with live display"""
    print("\nTesting camera with live display...")
    print("Press 'q' to quit the camera test")
    
    cap = cv2.VideoCapture(0)
    
    if not cap.isOpened():
        print("❌ Cannot open camera for display test")
        return False
    
    # Set camera properties
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    cap.set(cv2.CAP_PROP_FPS, 30)
    
    frame_count = 0
    start_time = time.time()
    
    try:
        while True:
            ret, frame = cap.read()
            
            if not ret:
                print("❌ Cannot read frame")
                break
            
            frame_count += 1
            
            # Calculate FPS
            elapsed_time = time.time() - start_time
            if elapsed_time > 0:
                fps = frame_count / elapsed_time
                cv2.putText(frame, f"FPS: {fps:.1f}", (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            
            # Add instructions
            cv2.putText(frame, "Press 'q' to quit", (10, frame.shape[0] - 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            cv2.imshow('Camera Test', frame)
            
            # Check for 'q' key press
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
    
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    
    except Exception as e:
        print(f"❌ Error during display test: {e}")
    
    finally:
        cap.release()
        cv2.destroyAllWindows()
        print(f"✅ Display test completed. Processed {frame_count} frames")
    
    return True

def check_opencv_version():
    """Check OpenCV version and build info"""
    print("OpenCV Information:")
    print(f"  Version: {cv2.__version__}")
    print(f"  Build info: {cv2.getBuildInformation()[:200]}...")

def main():
    """Run all camera tests"""
    print("=" * 60)
    print("Camera Diagnostic Tool")
    print("=" * 60)
    
    # Check OpenCV
    check_opencv_version()
    print()
    
    # Test basic camera access
    if not test_camera_basic():
        print("\n❌ Basic camera test failed!")
        print("\nTroubleshooting steps:")
        print("1. Check if camera is connected")
        print("2. Close other applications using the camera")
        print("3. Check camera permissions in Windows Settings")
        print("4. Try a different camera or USB port")
        return False
    
    # Test multiple cameras
    working_cameras = test_multiple_cameras()
    
    if not working_cameras:
        print("\n❌ No working cameras found!")
        return False
    
    print(f"\n✅ Found {len(working_cameras)} working camera(s): {working_cameras}")
    
    # Test camera properties
    test_camera_properties()
    
    # Ask user if they want to test with display
    print("\nDo you want to test camera with live display? (y/n): ", end="")
    try:
        response = input().lower().strip()
        if response in ['y', 'yes']:
            test_camera_with_display()
    except KeyboardInterrupt:
        print("\nSkipping display test")
    
    print("\n" + "=" * 60)
    print("Camera diagnostic completed!")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nDiagnostic interrupted by user")
    except Exception as e:
        print(f"Unexpected error: {e}")
    
    input("\nPress Enter to exit...")
