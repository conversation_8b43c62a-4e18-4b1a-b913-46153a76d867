# Camera Troubleshooting Guide

If you're getting "Error accessing camera" message, follow these steps to resolve the issue.

## Quick Diagnosis

First, run the camera diagnostic tool:
```bash
py camera_test.py
```

This will help identify the specific issue with your camera.

## Common Solutions

### 1. Browser Permissions

**Chrome/Edge:**
1. Click the camera icon in the address bar
2. Select "Always allow" for camera access
3. Refresh the page

**Firefox:**
1. Click the shield icon in the address bar
2. Turn off "Enhanced Tracking Protection" for this site
3. Allow camera permissions when prompted

**Safari:**
1. Go to Safari > Preferences > Websites > Camera
2. Set permission to "Allow" for localhost

### 2. Windows Camera Permissions

1. **Open Windows Settings** (Windows + I)
2. Go to **Privacy & Security** > **Camera**
3. Make sure **"Camera access"** is turned ON
4. Make sure **"Let apps access your camera"** is turned ON
5. Make sure **"Let desktop apps access your camera"** is turned ON

### 3. Camera Driver Issues

1. **Update Camera Drivers:**
   - Right-click "This PC" > Properties > Device Manager
   - Expand "Cameras" or "Imaging devices"
   - Right-click your camera > Update driver

2. **Reinstall Camera:**
   - In Device Manager, right-click camera > Uninstall device
   - Restart computer (camera will be reinstalled automatically)

### 4. Other Applications Using Camera

**Close these applications if running:**
- Skype
- Teams
- Zoom
- OBS Studio
- Any video recording software
- Other browser tabs with camera access

**Check running processes:**
1. Press Ctrl + Shift + Esc (Task Manager)
2. Look for applications using camera
3. End those processes

### 5. Camera Hardware Issues

**Test with different cameras:**
- Built-in laptop camera
- External USB camera
- Phone camera (via USB debugging)

**Try different USB ports:**
- Use USB 2.0 ports instead of USB 3.0
- Try different USB cables
- Use powered USB hub if needed

### 6. Browser-Specific Issues

**Clear browser data:**
1. Press Ctrl + Shift + Delete
2. Clear "Cookies and site data"
3. Clear "Cached images and files"
4. Restart browser

**Try different browsers:**
- Chrome
- Firefox
- Edge
- Safari (Mac)

**Disable browser extensions:**
- Disable ad blockers
- Disable privacy extensions
- Try incognito/private mode

### 7. Network and Security Software

**Disable temporarily:**
- Antivirus camera protection
- Firewall camera blocking
- VPN software
- Corporate security software

### 8. Advanced Solutions

**Registry Fix (Windows - Advanced users only):**
```
1. Press Windows + R, type "regedit"
2. Navigate to: HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows Media Foundation\Platform
3. Create DWORD: EnableFrameServerMode
4. Set value to 0
5. Restart computer
```

**Camera Service Restart:**
```bash
# Run as Administrator in Command Prompt
net stop "Windows Camera Frame Server"
net start "Windows Camera Frame Server"
```

## Testing Steps

### Step 1: Basic Camera Test
```bash
py camera_test.py
```

### Step 2: Browser Test
1. Go to: https://webcamtests.com/
2. Click "Test my cam"
3. Allow permissions
4. Verify camera works

### Step 3: Application Test
1. Start the face swap app: `py app.py`
2. Open browser to http://localhost:5000
3. Click "Start Camera"
4. Allow permissions when prompted

## Error Messages and Solutions

### "Camera not found"
- Check camera connections
- Update drivers
- Try different camera index in code

### "Permission denied"
- Check browser permissions
- Check Windows privacy settings
- Run browser as administrator

### "Camera in use by another application"
- Close other camera applications
- Restart computer
- Check Task Manager for camera processes

### "Camera initialization failed"
- Update OpenCV: `py -m pip install --upgrade opencv-python`
- Try different camera resolution
- Check camera compatibility

## Alternative Solutions

### Use Phone as Camera
1. Install IP Webcam app on Android
2. Connect phone and computer to same WiFi
3. Use phone's IP address as camera source

### Use Virtual Camera
1. Install OBS Studio
2. Set up virtual camera
3. Use OBS virtual camera as source

### External Camera
1. Buy USB webcam
2. Ensure USB 2.0/3.0 compatibility
3. Install manufacturer drivers

## Still Having Issues?

If none of these solutions work:

1. **Check system requirements:**
   - Windows 10/11
   - Updated drivers
   - Sufficient RAM (4GB+)

2. **Try minimal test:**
   ```python
   import cv2
   cap = cv2.VideoCapture(0)
   print("Camera opened:", cap.isOpened())
   cap.release()
   ```

3. **Contact support with:**
   - Operating system version
   - Camera model
   - Browser version
   - Error messages
   - Results from camera_test.py

## Prevention Tips

- Keep camera drivers updated
- Don't run multiple camera applications simultaneously
- Regularly clear browser cache
- Keep Windows privacy settings configured
- Use quality USB cables for external cameras
