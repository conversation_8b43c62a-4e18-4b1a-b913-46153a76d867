<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real-Time Face Swap Application</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Control Panel -->
            <div class="col-md-3 bg-dark text-white p-4">
                <h2 class="mb-4"><i class="fas fa-exchange-alt"></i> Face Swap Control</h2>
                
                <!-- Target Face Upload -->
                <div class="card bg-secondary mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-user"></i> Target Face</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="targetFaceInput" class="form-label">Upload Target Face Image</label>
                            <input type="file" class="form-control" id="targetFaceInput" accept="image/*">
                        </div>
                        <button class="btn btn-primary w-100" id="uploadTargetBtn">
                            <i class="fas fa-upload"></i> Upload Target Face
                        </button>
                        <div id="uploadStatus" class="mt-2"></div>
                    </div>
                </div>
                
                <!-- Face Swap Controls -->
                <div class="card bg-secondary mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-cogs"></i> Swap Controls</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="enableSwapToggle">
                            <label class="form-check-label" for="enableSwapToggle">
                                Enable Face Swap
                            </label>
                        </div>
                        <div class="mb-3">
                            <label for="confidenceSlider" class="form-label">Detection Confidence</label>
                            <input type="range" class="form-range" id="confidenceSlider" min="0.1" max="1" step="0.1" value="0.5">
                            <span id="confidenceValue">0.5</span>
                        </div>
                    </div>
                </div>
                
                <!-- Status Panel -->
                <div class="card bg-secondary">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="status-item">
                            <span class="status-label">Connection:</span>
                            <span id="connectionStatus" class="badge bg-danger">Disconnected</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">Face Swap:</span>
                            <span id="swapStatus" class="badge bg-secondary">Disabled</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">FPS:</span>
                            <span id="fpsCounter" class="badge bg-info">0</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Video Display -->
            <div class="col-md-9 p-4">
                <div class="row">
                    <div class="col-12">
                        <h1 class="text-center mb-4">Real-Time Face Swap Application</h1>
                        <p class="text-center text-muted">Upload a target face image and enable face swap to replace faces in real-time video</p>

                        <!-- Camera Help Alert -->
                        <div class="alert alert-info alert-dismissible fade show" role="alert">
                            <h6><i class="fas fa-info-circle"></i> Camera Setup Required</h6>
                            <p class="mb-2">This application needs camera access to work. If you encounter issues:</p>
                            <ul class="mb-2">
                                <li>Allow camera permissions when prompted</li>
                                <li>Close other apps using the camera (Skype, Teams, etc.)</li>
                                <li>Check Windows Privacy Settings for camera access</li>
                            </ul>
                            <small>
                                <strong>Need help?</strong> Run <code>py camera_test.py</code> or check <code>CAMERA_TROUBLESHOOTING.md</code>
                            </small>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- Original Video -->
                    <div class="col-md-6">
                        <div class="video-container">
                            <h4 class="text-center mb-3">Original Video</h4>
                            <video id="originalVideo" autoplay muted playsinline></video>
                            <canvas id="originalCanvas" style="display: none;"></canvas>
                        </div>
                    </div>
                    
                    <!-- Processed Video -->
                    <div class="col-md-6">
                        <div class="video-container">
                            <h4 class="text-center mb-3">Face Swapped Video</h4>
                            <img id="processedVideo" class="processed-frame" alt="Processed video will appear here">
                        </div>
                    </div>
                </div>
                
                <!-- Camera Controls -->
                <div class="row mt-4">
                    <div class="col-12 text-center">
                        <button class="btn btn-success btn-lg me-3" id="startCameraBtn">
                            <i class="fas fa-video"></i> Start Camera
                        </button>
                        <button class="btn btn-danger btn-lg me-3" id="stopCameraBtn" disabled>
                            <i class="fas fa-video-slash"></i> Stop Camera
                        </button>
                        <button class="btn btn-info btn-lg" id="switchCameraBtn">
                            <i class="fas fa-sync-alt"></i> Switch Camera
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="{{ url_for('static', filename='app.js') }}"></script>
</body>
</html>
