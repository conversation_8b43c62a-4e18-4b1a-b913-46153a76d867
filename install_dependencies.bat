@echo off
echo Installing Face Swap Application Dependencies...
echo.

REM Check if Python is available
py --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python from https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python found. Installing dependencies...
echo.

REM Install basic dependencies
echo Installing basic dependencies...
py -m pip install Flask==2.3.3 opencv-python numpy Pillow flask-socketio python-socketio eventlet

if %errorlevel% neq 0 (
    echo.
    echo Error installing basic dependencies. Trying alternative approach...
    py -m ensurepip --upgrade
    py -m pip install --upgrade pip
    py -m pip install Flask opencv-python numpy Pillow flask-socketio
)

echo.
echo Installing advanced dependencies for realistic face swapping...
py -m pip install dlib scipy

echo.
echo Downloading face landmarks model for advanced face swapping...
py download_models.py

echo.
echo Installing additional optional dependencies...
py -m pip install mediapipe face-recognition imutils

echo.
echo Installation completed!
echo.
echo To run the application:
echo 1. Double-click run_app.bat
echo 2. Or run: py app.py
echo 3. Open browser to http://localhost:5000
echo.
pause
