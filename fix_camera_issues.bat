@echo off
echo Camera Issues Quick Fix Script
echo ================================
echo.

echo Step 1: Stopping camera-related processes...
taskkill /f /im "WindowsCamera.exe" 2>nul
taskkill /f /im "Skype.exe" 2>nul
taskkill /f /im "Teams.exe" 2>nul
taskkill /f /im "Zoom.exe" 2>nul
echo Done.

echo.
echo Step 2: Restarting Windows Camera services...
net stop "Windows Camera Frame Server" 2>nul
timeout /t 2 /nobreak >nul
net start "Windows Camera Frame Server" 2>nul
echo Done.

echo.
echo Step 3: Clearing browser camera cache...
echo Please close all browser windows and press any key to continue...
pause >nul

REM Clear Chrome camera permissions
if exist "%LOCALAPPDATA%\Google\Chrome\User Data\Default\Preferences" (
    echo Clearing Chrome camera permissions...
    powershell -Command "(Get-Content '%LOCALAPPDATA%\Google\Chrome\User Data\Default\Preferences') -replace '\"camera\":\{[^}]*\}', '\"camera\":{}' | Set-Content '%LOCALAPPDATA%\Google\Chrome\User Data\Default\Preferences'"
)

echo.
echo Step 4: Testing camera...
py camera_test.py

echo.
echo Step 5: Opening camera privacy settings...
start ms-settings:privacy-webcam

echo.
echo Quick Fix Complete!
echo.
echo If camera still doesn't work:
echo 1. Restart your computer
echo 2. Update camera drivers
echo 3. Check CAMERA_TROUBLESHOOTING.md for detailed help
echo.
pause
