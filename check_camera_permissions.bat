@echo off
echo Checking Camera Permissions and Settings...
echo.

REM Check if camera privacy settings are enabled
echo Checking Windows Camera Privacy Settings...
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\CapabilityAccessManager\ConsentStore\webcam" /v Value 2>nul
if %errorlevel% equ 0 (
    echo Camera access appears to be configured in Windows
) else (
    echo Warning: Camera privacy settings may need configuration
)

echo.
echo Opening Windows Camera Privacy Settings...
start ms-settings:privacy-webcam

echo.
echo Please check the following in the opened settings window:
echo 1. "Camera access for this device" should be ON
echo 2. "Allow apps to access your camera" should be ON  
echo 3. "Allow desktop apps to access your camera" should be ON
echo.

echo Testing camera with Windows Camera app...
start microsoft.windows.camera:

echo.
echo If the Windows Camera app works, your camera hardware is fine.
echo If it doesn't work, you may have a hardware or driver issue.
echo.

pause
