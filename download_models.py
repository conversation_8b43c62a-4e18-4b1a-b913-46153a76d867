#!/usr/bin/env python3
"""
Download required models for advanced face swapping
"""

import os
import urllib.request
import bz2
import sys

def download_dlib_model():
    """Download and extract dlib face landmarks model"""
    model_url = "http://dlib.net/files/shape_predictor_68_face_landmarks.dat.bz2"
    model_file = "shape_predictor_68_face_landmarks.dat"
    compressed_file = "shape_predictor_68_face_landmarks.dat.bz2"
    
    if os.path.exists(model_file):
        print(f"✓ {model_file} already exists")
        return True
    
    print("Downloading dlib 68-point face landmarks model...")
    print("This may take a few minutes (file size: ~65MB)")
    
    try:
        # Download with progress
        def progress_hook(block_num, block_size, total_size):
            downloaded = block_num * block_size
            if total_size > 0:
                percent = min(100, (downloaded * 100) // total_size)
                sys.stdout.write(f"\rDownloading: {percent}% ({downloaded // 1024 // 1024}MB / {total_size // 1024 // 1024}MB)")
                sys.stdout.flush()
        
        urllib.request.urlretrieve(model_url, compressed_file, progress_hook)
        print("\n✓ Download completed")
        
        # Extract the file
        print("Extracting model file...")
        with bz2.BZ2File(compressed_file, 'rb') as f_in:
            with open(model_file, 'wb') as f_out:
                f_out.write(f_in.read())
        
        # Clean up compressed file
        os.remove(compressed_file)
        
        print(f"✓ {model_file} extracted successfully")
        return True
        
    except Exception as e:
        print(f"\n✗ Error downloading model: {e}")
        print("\nManual download instructions:")
        print(f"1. Download: {model_url}")
        print(f"2. Extract to: {model_file}")
        print("3. Place in the project root directory")
        return False

def verify_model():
    """Verify the downloaded model works"""
    model_file = "shape_predictor_68_face_landmarks.dat"
    
    if not os.path.exists(model_file):
        print(f"✗ {model_file} not found")
        return False
    
    try:
        import dlib
        predictor = dlib.shape_predictor(model_file)
        print(f"✓ {model_file} is valid and working")
        return True
    except Exception as e:
        print(f"✗ Error loading model: {e}")
        return False

def main():
    """Main function"""
    print("=" * 60)
    print("Face Swap Models Downloader")
    print("=" * 60)
    
    # Check if dlib is installed
    try:
        import dlib
        print("✓ dlib is installed")
    except ImportError:
        print("✗ dlib is not installed")
        print("Installing dlib...")
        try:
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "dlib"])
            print("✓ dlib installed successfully")
        except Exception as e:
            print(f"✗ Failed to install dlib: {e}")
            print("Please install dlib manually:")
            print("pip install dlib")
            return False
    
    # Download dlib model
    if download_dlib_model():
        # Verify model
        if verify_model():
            print("\n" + "=" * 60)
            print("✓ All models downloaded and verified!")
            print("✓ Advanced face swapping is now available")
            print("=" * 60)
            return True
    
    print("\n" + "=" * 60)
    print("✗ Model download failed")
    print("The application will use fallback face swapping")
    print("=" * 60)
    return False

if __name__ == "__main__":
    main()
